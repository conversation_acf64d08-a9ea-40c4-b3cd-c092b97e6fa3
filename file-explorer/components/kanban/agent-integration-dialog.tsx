"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useBoard, Agent } from "./board-context"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog"
import { Cpu } from "lucide-react" // Using Cpu icon instead of Robot

export function AgentIntegrationDialog() {
  const { activeBoard, updateAgents } = useBoard()
  const [agents, setAgents] = useState<Agent[]>(activeBoard?.agents || [])
  const [newAgentName, setNewAgentName] = useState("")
  const [newAgentType, setNewAgentType] = useState("worker")
  const [newAgentCapabilities, setNewAgentCapabilities] = useState("")

  const handleAddAgent = () => {
    if (!newAgentName || !newAgentType) return

    const newAgent: Agent = {
      id: `agent-${Date.now()}`,
      name: newAgentName,
      type: newAgentType,
      status: "idle",
      capabilities: newAgentCapabilities.split(",").map(cap => cap.trim()).filter(Boolean),
      resourceUsage: {
        cpu: 0,
        memory: 0,
        tokens: 0,
      },
    }

    const updatedAgents = [...agents, newAgent]
    setAgents(updatedAgents)

    if (activeBoard) {
      updateAgents(activeBoard.id, updatedAgents)
    }

    // Reset form
    setNewAgentName("")
    setNewAgentType("worker")
    setNewAgentCapabilities("")
  }

  const handleRemoveAgent = (agentId: string) => {
    const updatedAgents = agents.filter(agent => agent.id !== agentId)
    setAgents(updatedAgents)

    if (activeBoard) {
      updateAgents(activeBoard.id, updatedAgents)
    }
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            console.log("Agent Integration Dialog button clicked");
          }}
        >
          <Cpu className="h-4 w-4 mr-2" />
          Agents
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] h-auto max-h-[80vh] p-6">
        <DialogHeader>
          <DialogTitle>AI Agent Integration</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Current Agents</h3>
            {agents.length === 0 ? (
              <div className="text-sm text-muted-foreground">No agents configured</div>
            ) : (
              <div className="space-y-2">
                {agents.map((agent) => (
                  <div key={agent.id} className="flex items-center justify-between p-2 border border-border rounded-md">
                    <div>
                      <div className="font-medium">{agent.name}</div>
                      <div className="text-xs text-muted-foreground">Type: {agent.type}</div>
                    </div>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log("Remove Agent button clicked for", agent.name);
                        handleRemoveAgent(agent.id);
                      }}
                    >
                      Remove
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="border-t border-border pt-4">
            <h3 className="text-sm font-medium mb-2">Add New Agent</h3>
            <div className="space-y-3">
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="agentName" className="text-right text-sm">
                  Name
                </label>
                <Input
                  id="agentName"
                  value={newAgentName}
                  onChange={(e) => setNewAgentName(e.target.value)}
                  className="col-span-3"
                  placeholder="Agent name"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="agentType" className="text-right text-sm">
                  Type
                </label>
                <Input
                  id="agentType"
                  value={newAgentType}
                  onChange={(e) => setNewAgentType(e.target.value)}
                  className="col-span-3"
                  placeholder="e.g. worker, reviewer, etc."
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="agentCapabilities" className="text-right text-sm">
                  Capabilities
                </label>
                <Input
                  id="agentCapabilities"
                  value={newAgentCapabilities}
                  onChange={(e) => setNewAgentCapabilities(e.target.value)}
                  className="col-span-3"
                  placeholder="Comma-separated capabilities"
                />
              </div>
              <div className="flex justify-end">
                <Button onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log("Add Agent button clicked");
                  handleAddAgent();
                }}>
                  Add Agent
                </Button>
              </div>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log("Close button clicked");
              // The Dialog component will handle closing automatically
            }}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}