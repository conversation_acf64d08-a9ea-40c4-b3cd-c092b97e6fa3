"use client"

import { useState, useEffect, useRef } from "react"
import { ChevronDown, ChevronRight, Folder, FolderOpen, Search, Settings, Plus, MoreHorizontal, File as FileIconLucide, Edit3, Trash2, FolderPlus, Clock, Save, Play, Eye, EyeOff, Grid, List } from "lucide-react"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import { PRDUploadUI } from "./intake/prd-upload-ui"
import { prdIntakeService, PRDValidationResult, PRDParseResult } from "./intake/prd-intake-service"
import { TaskmasterOrchestrationUI } from "./orchestrators/taskmaster-orchestration-ui"
import { OrchestrationResult } from "./orchestrators/kanban-task-orchestrator"

// Improved code file type icons with distinctive symbols - LARGER and more visible
export const CodeFileIcon = ({
  extension,
  className,
}: {
  extension: string
  className?: string
}) => {
  // Define icon colors for different file types
  const getIconColor = () => {
    const colors: Record<string, string> = {
      js: "text-yellow-400",
      ts: "text-blue-500",
      jsx: "text-cyan-400",
      tsx: "text-cyan-500",
      php: "text-indigo-400",
      py: "text-green-500",
      rb: "text-red-500",
      java: "text-amber-600",
      c: "text-blue-400",
      cpp: "text-blue-600",
      cs: "text-purple-500",
      go: "text-cyan-500",
      rs: "text-orange-600",
      html: "text-orange-500",
      css: "text-blue-400",
      scss: "text-pink-500",
      json: "text-yellow-600",
      yaml: "text-purple-400",
      yml: "text-purple-400",
      xml: "text-orange-400",
      md: "text-gray-400",
      sql: "text-blue-300",
      sh: "text-gray-400",
      bash: "text-gray-400",
      graphql: "text-pink-600",
      vue: "text-emerald-500",
      svelte: "text-red-600",
      dart: "text-cyan-600",
      kt: "text-purple-400",
      swift: "text-orange-500",
    }

    return colors[extension] || "text-muted-foreground"
  }

  // Get background color for icon
  const getIconBgColor = () => {
    const bgColors: Record<string, string> = {
      js: "bg-yellow-400/15",
      ts: "bg-blue-500/15",
      jsx: "bg-cyan-400/15",
      tsx: "bg-cyan-500/15",
      php: "bg-indigo-400/15",
      py: "bg-green-500/15",
      rb: "bg-red-500/15",
      java: "bg-amber-600/15",
      c: "bg-blue-400/15",
      cpp: "bg-blue-600/15",
      cs: "bg-purple-500/15",
      go: "bg-cyan-500/15",
      rs: "bg-orange-600/15",
      html: "bg-orange-500/15",
      css: "bg-blue-400/15",
      scss: "bg-pink-500/15",
      json: "bg-yellow-600/15",
      yaml: "bg-purple-400/15",
      yml: "bg-purple-400/15",
      xml: "bg-orange-400/15",
      md: "bg-gray-400/15",
      sql: "bg-blue-300/15",
      sh: "bg-gray-400/15",
      bash: "bg-gray-400/15",
      graphql: "bg-pink-600/15",
      vue: "bg-emerald-500/15",
      svelte: "bg-red-600/15",
      dart: "bg-cyan-600/15",
      kt: "bg-purple-400/15",
      swift: "bg-orange-500/15",
    }

    return bgColors[extension] || "bg-muted-foreground/15"
  }

  // Simple, clear file type indicators
  const getFileIcon = () => {
    switch (extension) {
      case "js":
        return <div className="font-bold">JS</div>
      case "ts":
        return <div className="font-bold">TS</div>
      case "jsx":
        return <div className="font-bold">JSX</div>
      case "tsx":
        return <div className="font-bold">TSX</div>
      case "php":
        return <div className="font-bold">PHP</div>
      case "py":
        return <div className="font-bold">PY</div>
      case "rb":
        return <div className="font-bold">RB</div>
      case "java":
        return <div className="font-bold">JV</div>
      case "c":
        return <div className="font-bold">C</div>
      case "cpp":
        return <div className="font-bold">C++</div>
      case "cs":
        return <div className="font-bold">C#</div>
      case "go":
        return <div className="font-bold">GO</div>
      case "rs":
        return <div className="font-bold">RS</div>
      case "html":
        return <div className="font-bold">{"<>"}</div>
      case "css":
        return <div className="font-bold">CSS</div>
      case "scss":
        return <div className="font-bold">SC</div>
      case "json":
        return <div className="font-bold">{"{}"}</div>
      case "yaml":
      case "yml":
        return <div className="font-bold">YML</div>
      case "xml":
        return <div className="font-bold">XML</div>
      case "md":
        return <div className="font-bold">MD</div>
      case "sql":
        return <div className="font-bold">SQL</div>
      case "sh":
      case "bash":
        return <div className="font-bold">SH</div>
      case "graphql":
        return <div className="font-bold">GQL</div>
      case "vue":
        return <div className="font-bold">VUE</div>
      case "svelte":
        return <div className="font-bold">SV</div>
      case "dart":
        return <div className="font-bold">DRT</div>
      case "kt":
        return <div className="font-bold">KT</div>
      case "swift":
        return <div className="font-bold">SWF</div>
      default:
        return <div className="font-bold">DOC</div>
    }
  }

  return (
    <div
      className={cn(
        "flex items-center justify-center text-xs w-5 h-5 rounded-sm",
        getIconColor(),
        getIconBgColor(),
        className,
      )}
    >
      {getFileIcon()}
    </div>
  )
}

// FileSystemItem type definition
export interface FileSystemItem {
  id: number | string
  name: string
  type: string
  path?: string
  content?: string
  size?: number
  modified?: Date
  expanded?: boolean
  files?: FileSystemItem[]
}

// Empty project structure
const emptyProjects: FileSystemItem[] = []

// Utility function to ensure unique IDs for file system items
const ensureUniqueIds = (items: any[], parentPath: string = '', baseId: number = Date.now()): any[] => {
  return items.map((item, index) => {
    const uniqueId = `${baseId}-${parentPath}-${item.name}-${index}`.replace(/[^a-zA-Z0-9-]/g, '-');
    const processedItem = {
      ...item,
      id: uniqueId,
    };

    // Recursively process nested files if they exist
    if (item.files && Array.isArray(item.files)) {
      processedItem.files = ensureUniqueIds(item.files, `${parentPath}/${item.name}`, baseId);
    }

    return processedItem;
  });
};

// Recursive component to render file tree
const FileTreeItem = ({
  item,
  level = 0,
  onToggle,
  onSelect,
  selectedFile,
}: {
  item: any
  level?: number
  onToggle: (id: number | string) => void
  onSelect: (file: any) => void
  selectedFile: any | null
}) => {
  const isFolder = item.type === "folder" || Array.isArray(item.files)
  const indent = level * 16
  const isSelected = selectedFile && selectedFile.id === item.id

  return (
    <>
      <div
        className={cn(
          "flex items-center py-1 px-2 text-sm rounded-md cursor-pointer group",
          "transition-colors duration-100",
          isSelected ? "bg-accent text-accent-foreground" : "hover:bg-accent/20",
        )}
        style={{ paddingLeft: `${indent + 8}px` }}
        onClick={(e) => {
          e.stopPropagation()
          if (isFolder) {
            onToggle(item.id)
          } else {
            onSelect(item)
          }
        }}
      >
        {isFolder ? (
          item.expanded ? (
            <ChevronDown className="sidebar-icon mr-1.5 text-muted-foreground flex-shrink-0" />
          ) : (
            <ChevronRight className="sidebar-icon mr-1.5 text-muted-foreground flex-shrink-0" />
          )
        ) : (
          <div className="w-5 mr-1.5 flex-shrink-0" />
        )}

        {isFolder ? (
          item.expanded ? (
            <FolderOpen className="file-icon mr-1.5 text-blue-400 flex-shrink-0" />
          ) : (
            <Folder className="file-icon mr-1.5 text-blue-400 flex-shrink-0" />
          )
        ) : (
          <CodeFileIcon extension={item.type} className="mr-1.5 flex-shrink-0" />
        )}

        <span className="truncate">{item.name}</span>

        {!isFolder && (
          <div className="ml-auto opacity-0 group-hover:opacity-100">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-5 w-5 text-muted-foreground">
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation()
                    console.log(`Rename ${item.name}`)
                  }}
                >
                  Rename
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation()
                    console.log(`Delete ${item.name}`)
                  }}
                >
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {isFolder &&
        item.expanded &&
        item.files &&
        item.files.map((file: any) => (
          <FileTreeItem
            key={file.id}
            item={file}
            level={level + 1}
            onToggle={onToggle}
            onSelect={onSelect}
            selectedFile={selectedFile}
          />
        ))}
    </>
  )
}

export default function FileSidebar({ onFileSelect }: { onFileSelect: (file: any) => void }) {
  const [projects, setProjects] = useState(emptyProjects)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedFile, setSelectedFile] = useState<any>(null)
  const [showCreateProjectDialog, setShowCreateProjectDialog] = useState(false)
  const [newProjectName, setNewProjectName] = useState("")
  const [recentProjects, setRecentProjects] = useState<Array<{name: string, path: string, lastOpened: number}>>([])
  const [showUnsavedChangesDialog, setShowUnsavedChangesDialog] = useState(false)
  const [pendingProjectSwitch, setPendingProjectSwitch] = useState<{name: string, path: string} | null>(null)
  const [showPRDDialog, setShowPRDDialog] = useState(false)
  const [showExplorerSettings, setShowExplorerSettings] = useState(false)

  // ✅ Step 1: Project folder path state management
  const [projectFolderPath, setProjectFolderPath] = useState<string | null>(null)

  // Explorer settings state
  const [explorerSettings, setExplorerSettings] = useState({
    showHiddenFiles: false,
    autoExpandFolders: true,
    showFileExtensions: true,
    compactView: false,
    showFileIcons: true,
    sortBy: 'name' as 'name' | 'modified' | 'size' | 'type',
    sortOrder: 'asc' as 'asc' | 'desc'
  })
  const [prdValidated, setPrdValidated] = useState(false)
  const [currentProjectPath, setCurrentProjectPath] = useState<string | null>(null)
  const [showOrchestrationDialog, setShowOrchestrationDialog] = useState(false)
  const { toast } = useToast()

  // Load Explorer settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('explorerSettings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setExplorerSettings(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.warn('Failed to parse saved explorer settings:', error);
      }
    }
  }, []);

  // Load recent projects on component mount
  useEffect(() => {
    loadRecentProjects()
  }, [])

  const loadRecentProjects = async () => {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const { settingsManager } = await import('./settings/settings-manager')
        const recentProjectsList = await settingsManager.getRecentProjects()
        setRecentProjects(recentProjectsList || [])
      }
    } catch (error) {
      console.warn('Failed to load recent projects:', error)
    }
  }

  const checkForUnsavedChanges = async (): Promise<boolean> => {
    try {
      // Check if Monaco editor has unsaved changes
      if (typeof window !== 'undefined' && window.monaco) {
        const models = window.monaco.editor.getModels()
        for (const model of models) {
          if (model.isAttachedToEditor() && model.getValue() !== model.getAlternativeVersionId()) {
            return true // Has unsaved changes
          }
        }
      }
      return false
    } catch (error) {
      console.warn('Failed to check for unsaved changes:', error)
      return false
    }
  }

  const saveUnsavedChanges = async (): Promise<boolean> => {
    try {
      if (typeof window !== 'undefined' && window.monaco && window.electronAPI) {
        const models = window.monaco.editor.getModels()
        let saveCount = 0

        for (const model of models) {
          if (model.isAttachedToEditor()) {
            const uri = model.uri.toString()
            const content = model.getValue()

            // Extract file path from URI
            const filePath = uri.replace('file://', '')

            try {
              const result = await window.electronAPI.saveFile(filePath, content)
              if (result.success) {
                saveCount++
              } else {
                console.error('Failed to save file:', filePath, result.error)
              }
            } catch (saveError) {
              console.error('Error saving file:', filePath, saveError)
            }
          }
        }

        if (saveCount > 0) {
          toast({
            title: "Files Saved",
            description: `Saved ${saveCount} file(s) before switching projects.`,
          })
        }

        return true
      }
      return true
    } catch (error) {
      console.error('Failed to save unsaved changes:', error)
      toast({
        title: "Save Error",
        description: "Failed to save some files. Please save manually.",
        variant: "destructive",
      })
      return false
    }
  }

  const switchToProject = async (projectName: string, projectPath: string) => {
    try {
      // Check for unsaved changes
      const hasUnsavedChanges = await checkForUnsavedChanges()

      if (hasUnsavedChanges) {
        // Show confirmation dialog
        setPendingProjectSwitch({ name: projectName, path: projectPath })
        setShowUnsavedChangesDialog(true)
        return
      }

      // No unsaved changes, proceed with switch
      await performProjectSwitch(projectName, projectPath)
    } catch (error) {
      console.error('Error switching projects:', error)
      toast({
        title: "Project Switch Error",
        description: "Failed to switch projects. Please try again.",
        variant: "destructive",
      })
    }
  }

  const performProjectSwitch = async (projectName: string, projectPath: string) => {
    try {
      // Load the selected project
      await loadProjectFromPath(projectPath, projectName)

      // Update recent projects list
      await updateRecentProjects(projectName, projectPath)

      toast({
        title: "Project Switched",
        description: `Switched to ${projectName}`,
      })
    } catch (error) {
      console.error('Error performing project switch:', error)
      toast({
        title: "Project Switch Error",
        description: "Failed to load the selected project.",
        variant: "destructive",
      })
    }
  }

  const updateRecentProjects = async (projectName: string, projectPath: string) => {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const { settingsManager } = await import('./settings/settings-manager')
        await settingsManager.addRecentProject(projectName, projectPath)
        await loadRecentProjects() // Refresh the list
      }
    } catch (error) {
      console.warn('Failed to update recent projects:', error)
    }
  }

  const handleUnsavedChangesConfirm = async (saveChanges: boolean) => {
    setShowUnsavedChangesDialog(false)

    if (!pendingProjectSwitch) return

    try {
      if (saveChanges) {
        const saveSuccess = await saveUnsavedChanges()
        if (!saveSuccess) {
          // User can choose to continue anyway or cancel
          return
        }
      }

      await performProjectSwitch(pendingProjectSwitch.name, pendingProjectSwitch.path)
    } finally {
      setPendingProjectSwitch(null)
    }
  }

  const toggleFolder = async (id: number | string) => {
    const updateFolderState = async (items: any[]): Promise<any[]> => {
      const updatedItems = [];

      for (const item of items) {
        if (item.id === id) {
          const newExpanded = !item.expanded;

          // If expanding a folder that has a path (from file system) and no files loaded yet
          if (newExpanded && item.path && item.type === 'folder' && (!item.files || item.files.length === 0)) {
            try {
              if (window.electronAPI) {
                const result = await window.electronAPI.readDirectory(item.path);
                if (result.success && result.items) {
                  const files = ensureUniqueIds(result.items.map((fsItem: any) => ({
                    ...fsItem,
                    files: fsItem.type === 'folder' ? [] : undefined
                  })), item.path || '', Date.now());
                  updatedItems.push({ ...item, expanded: newExpanded, files });
                } else {
                  updatedItems.push({ ...item, expanded: newExpanded });
                }
              } else {
                updatedItems.push({ ...item, expanded: newExpanded });
              }
            } catch (error) {
              console.error('Error loading folder contents:', error);
              updatedItems.push({ ...item, expanded: newExpanded });
            }
          } else {
            updatedItems.push({ ...item, expanded: newExpanded });
          }
        } else if (item.files && Array.isArray(item.files)) {
          const updatedFiles = await updateFolderState(item.files);
          updatedItems.push({
            ...item,
            files: updatedFiles,
          });
        } else {
          updatedItems.push(item);
        }
      }

      return updatedItems;
    }

    const updatedProjects = await updateFolderState(projects);
    setProjects(updatedProjects);
  }

  const handleFileSelect = async (file: any) => {
    console.log("File selected:", file);
    setSelectedFile(file);

    // Don't try to load content for folders
    if (file.type === 'folder') {
      console.log("Selected item is a folder, not loading content");
      onFileSelect(file);
      return;
    }

    // If the file already has content, use it
    if (file.content !== undefined) {
      console.log("File already has content, using cached version");
      onFileSelect(file);
      return;
    }

    // If the file has a path (from file system), load its content
    if (file.path && window.electronAPI) {
      console.log("Loading file content from disk:", file.path);
      try {
        const result = await window.electronAPI.readFile(file.path);
        console.log("File read result:", result.success ? "success" : "failed");

        if (result.success) {
          const fileWithContent = {
            ...file,
            content: result.content
          };
          console.log("File content loaded, length:", result.content.length);
          onFileSelect(fileWithContent);
        } else {
          console.error("Failed to read file:", result.error);
          // Still select the file even if we couldn't read it
          onFileSelect(file);
        }
      } catch (error) {
        console.error('Error reading file:', error);
        // Still select the file even if we couldn't read it
        onFileSelect(file);
      }
    } else {
      console.log("File has no path or electronAPI not available");
      onFileSelect(file);
    }
  }

  const createProject = async () => {
    if (!newProjectName.trim()) {
      return;
    }

    const projectName = newProjectName.trim();

    try {
      // Step 1: Open Folder Picker Dialog
      if (!window.electronAPI) {
        console.error("Electron API not available");
        alert('Project creation is only available in the desktop app.');
        return;
      }

      const selectedFolder = await window.electronAPI.selectFolder();
      if (!selectedFolder || !selectedFolder.success || !selectedFolder.path) {
        return; // Exit if user cancels
      }

      // ✅ Step 1: Capture and Persist Project Folder Path
      setProjectFolderPath(selectedFolder.path);
      console.log(`✅ Project folder captured: ${selectedFolder.path}`);

      // Step 2: Create the New Project Folder
      const projectPath = `${selectedFolder.path}/${projectName}`;
      setCurrentProjectPath(projectPath);

      // First create the directory structure
      const directoryResult = await window.electronAPI.createFile(`${projectPath}/.project`, '');
      if (!directoryResult || !directoryResult.success) {
        alert(`Failed to create project directory: ${directoryResult?.error || 'Unknown error'}`);
        return;
      }

      // ✅ Step 2: Immediately Register Project with Global Context
      try {
        // Set as active project immediately after directory creation
        const { activeProjectService } = await import('../services/active-project-service');
        activeProjectService.setActiveProject(projectPath, projectName);
        console.log(`✅ Project activated in global context: ${projectName} (${projectPath})`);

        // Register project with settings manager with enhanced config
        const { settingsManager } = await import('./settings/settings-manager');
        await settingsManager.createProject(projectName, projectPath);
        console.log(`✅ Project registered with settings manager: ${projectName}`);

        // ✅ Verify active project is properly set
        const verifyActiveProject = activeProjectService.getActiveProject();
        if (!verifyActiveProject?.path) {
          throw new Error('Active project verification failed - project not properly registered');
        }
        console.log(`✅ Active project verification passed: ${verifyActiveProject.name} (${verifyActiveProject.path})`);
      } catch (error) {
        console.error('❌ Failed to register project in global context:', error);
        alert(`Failed to register project: ${error instanceof Error ? error.message : 'Unknown error'}`);
        return;
      }

      // ✅ Step 2.6: Require PRD before continuing (now with active project context)
      setShowCreateProjectDialog(false);

      // Validate active project before showing PRD dialog
      const isValidForPRD = await validateActiveProjectForPRD();
      if (isValidForPRD) {
        setShowPRDDialog(true);
      } else {
        // Cleanup if validation fails
        setCurrentProjectPath(null);
        setProjectFolderPath(null);
        return;
      }

      // The rest of project creation will continue after PRD is uploaded and validated
    } catch (error) {
      console.error('Error creating project:', error);
      alert(`Failed to create project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // ✅ Continue project creation after PRD validation
  const continueProjectCreation = async (projectPath: string, projectName: string) => {
    try {
      // Step 3: Initialize Taskmaster for the project
      console.log(`🔧 Initializing Taskmaster for project: ${projectName}`);
      try {
        const { taskmasterIntegrationService } = await import('../services/taskmaster-integration-service');
        const initResult = await taskmasterIntegrationService.initializeForProject(projectPath, projectName);

        if (initResult.success) {
          console.log(`✅ Taskmaster initialized successfully for ${projectName}`);
        } else {
          console.warn(`⚠️ Taskmaster initialization failed: ${initResult.error}`);
          // Continue with project creation even if Taskmaster fails
        }
      } catch (taskmasterError) {
        console.warn('⚠️ Taskmaster integration failed:', taskmasterError);
        // Continue with project creation even if Taskmaster fails
      }

      // Step 4: Write Initial File to Disk (README.md)
      const readmeContent = `# ${projectName}

A new software project.

## Development

Start developing your application here.

## Structure

- Source files go in appropriate directories
- Documentation in docs/
- Tests in tests/
- .taskmaster/ - AI task management (auto-generated)

## AI Task Management

This project includes integrated Taskmaster AI for automated task generation and management:
- Upload a PRD (Project Requirements Document) to generate tasks
- Tasks are automatically converted to Kanban boards
- AI agents execute tasks automatically

Use the "Tasks" button in the toolbar to access task orchestration features.

## Build

Add build instructions specific to your project type.
`;

      const readmeResult = await window.electronAPI.createFile(`${projectPath}/README.md`, readmeContent);
      if (!readmeResult || !readmeResult.success) {
        alert(`Failed to create README.md: ${readmeResult?.error || 'Unknown error'}`);
        return;
      }

      // Step 4: Project already registered in global context before PRD dialog
      // (No need to register again - already done in createProject function)

      // Step 5: Refresh File Explorer from Disk
      const updatedTree = await window.electronAPI.readDirectory(projectPath);
      if (updatedTree && updatedTree.success && updatedTree.items) {
        const baseId = Date.now();
        const newProject = {
          id: baseId,
          name: projectName,
          type: "folder" as const,
          path: projectPath,
          expanded: true,
          files: ensureUniqueIds(updatedTree.items.map((item: any) => ({
            ...item,
            files: item.type === 'folder' ? [] : undefined
          })), projectPath, baseId)
        };

        setProjects([...projects, newProject]);

        // Step 6: Active project already set before PRD dialog
        // (Project is already active in global context - no need to set again)
        console.log("✅ Project remains active in global context:", projectPath);
      }

      // Close dialog and reset form
      setNewProjectName("");
      setShowPRDDialog(false);
      setPrdValidated(false);
      setCurrentProjectPath(null);
      setProjectFolderPath(null); // ✅ Reset project folder path

      toast({
        title: "Project Created",
        description: `${projectName} created successfully with PRD validation.`,
      });

    } catch (error) {
      console.error('Error creating project:', error);
      alert(`Failed to create project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // ✅ Handle PRD upload completion
  const handlePRDUploaded = (filePath: string, validation: PRDValidationResult) => {
    console.log('✅ PRD uploaded successfully:', filePath);
    setPrdValidated(validation.isValid);

    if (validation.isValid) {
      toast({
        title: "PRD Validated",
        description: `PRD uploaded with quality score: ${validation.score}/100`,
      });
    }
  }

  // ✅ Handle PRD parsing completion
  const handlePRDParsed = (result: PRDParseResult) => {
    console.log('✅ PRD parsed with Taskmaster:', result);

    if (result.success && currentProjectPath) {
      // Continue with project creation now that PRD is parsed
      const projectName = newProjectName.trim();
      continueProjectCreation(currentProjectPath, projectName);

      toast({
        title: "PRD Parsed",
        description: `Generated ${result.taskCount} tasks from PRD`,
      });
    } else {
      toast({
        title: "PRD Parsing Failed",
        description: result.error || "Failed to parse PRD with Taskmaster",
        variant: "destructive",
      });
    }
  }

  // ✅ Handle PRD validation change
  const handlePRDValidationChange = (isValid: boolean) => {
    setPrdValidated(isValid);
  }

  // ✅ Validate active project before showing PRD dialog
  const validateActiveProjectForPRD = async (): Promise<boolean> => {
    try {
      const { activeProjectService } = await import('../services/active-project-service');
      const activeProject = activeProjectService.getActiveProject();

      if (!activeProject?.path) {
        console.error('❌ PRD Dialog blocked: No active project in global context');
        alert('Error: No active project found. Please create a project first.');
        return false;
      }

      console.log(`✅ PRD Dialog validation passed: Active project ${activeProject.name} (${activeProject.path})`);
      return true;
    } catch (error) {
      console.error('❌ PRD Dialog validation failed:', error);
      alert('Error: Failed to validate active project context.');
      return false;
    }
  }

  // ✅ Handle orchestration completion
  const handleOrchestrationComplete = (result: OrchestrationResult) => {
    console.log('✅ Orchestration completed:', result);

    if (result.success) {
      toast({
        title: "Orchestration Complete",
        description: `Created ${result.boardsCreated} boards with ${result.cardsCreated} cards. Agents are now executing tasks.`,
      });
      setShowOrchestrationDialog(false);
    } else {
      toast({
        title: "Orchestration Failed",
        description: result.error || "Failed to orchestrate Taskmaster tasks",
        variant: "destructive",
      });
    }
  }

  // ✅ Handle orchestration trigger
  const handleStartOrchestration = () => {
    setShowOrchestrationDialog(true);
  }

  const handleCreateProject = () => {
    console.log("handleCreateProject called")
    setShowCreateProjectDialog(true)
  }

  const handleOpenProject = async () => {
    try {
      console.log("handleOpenProject called")
      // Check if we're in an Electron environment
      if (typeof window !== 'undefined') {
        // Check if electronAPI is available
        if (window.electronAPI) {
          console.log("Calling electronAPI.selectFolder")
          try {
            const result = await window.electronAPI.selectFolder();
            console.log("selectFolder result:", result)
            if (result && result.success && result.path && result.name) {
              await loadProjectFromPath(result.path, result.name);
            }
          } catch (err) {
            console.error("Error calling electronAPI.selectFolder:", err);
            alert('Failed to open project. Please check the console for details.');
          }
        } else {
          // Fallback for web environment - show a message
          console.log("electronAPI not found in window object")
          console.log("Window object keys:", Object.keys(window));
          alert('Open Project functionality is only available in the desktop app. The Electron API is not properly initialized.');
        }
      } else {
        // This should never happen in a browser environment
        console.log("Window is undefined")
        alert('Cannot access browser window object.');
      }
    } catch (error) {
      console.error('Error opening project:', error);
      alert('Failed to open project. Please try again.');
    }
  }

  const loadProjectFromPath = async (projectPath: string, projectName: string) => {
    try {
      console.log("Loading project from path:", projectPath, projectName);

      // Ensure Taskmaster is initialized for this project
      try {
        const { taskmasterIntegrationService } = await import('../services/taskmaster-integration-service');
        const isInit = await taskmasterIntegrationService.isInitialized(projectPath);

        if (!isInit) {
          console.log(`🔧 Auto-initializing Taskmaster for existing project: ${projectName}`);
          const initResult = await taskmasterIntegrationService.initializeForProject(projectPath, projectName);

          if (initResult.success) {
            console.log(`✅ Taskmaster auto-initialized for ${projectName}`);
          } else {
            console.warn(`⚠️ Taskmaster auto-initialization failed: ${initResult.error}`);
          }
        } else {
          console.log(`✅ Taskmaster already initialized for ${projectName}`);
        }
      } catch (taskmasterError) {
        console.warn('⚠️ Taskmaster integration check failed:', taskmasterError);
      }

      // Set as active project for Agent System
      try {
        // Only set active project in Electron environment
        if (typeof window !== 'undefined' && window.electronAPI) {
          const { activeProjectService } = await import('../services/active-project-service');
          activeProjectService.setActiveProject(projectPath, projectName);
          console.log("Set active project for agent system:", projectPath);

          // Register project with settings manager
          try {
            const { settingsManager } = await import('./settings/settings-manager');
            await settingsManager.createProject(projectName, projectPath);
            console.log("Registered opened project with settings manager");
          } catch (settingsError) {
            console.warn("Failed to register project with settings manager:", settingsError);
            // Continue anyway - project opening succeeded
          }
        }
      } catch (error) {
        console.warn("Failed to set active project:", error);
      }

      if (window.electronAPI) {
        console.log("Calling electronAPI.readDirectory");
        const result = await window.electronAPI.readDirectory(projectPath);
        console.log("readDirectory result:", result);

        if (result.success && result.items) {
          console.log("Successfully read directory, creating project structure");
          const baseId = Date.now();
          const newProject = {
            id: baseId,
            name: projectName,
            type: "folder" as const,
            path: projectPath,
            expanded: true,
            files: ensureUniqueIds(await Promise.all(result.items.map(async (item: any) => {
              if (item.type === 'folder') {
                return {
                  ...item,
                  files: [] // We'll load subdirectories on demand
                };
              }
              return item;
            })), projectPath, baseId)
          };

          console.log("New project structure:", newProject);

          // Check if project is already open
          const existingProject = projects.find(p => p.path === projectPath);
          if (!existingProject) {
            console.log("Adding new project to projects list");
            setProjects([...projects, newProject]);
          } else {
            console.log("Project already exists in projects list");
          }
        } else {
          console.error("Failed to read directory:", result.error);
          alert(`Failed to read directory: ${result.error || 'Unknown error'}`);
        }
      } else {
        console.error("electronAPI not available");
        alert('Failed to load project: Electron API not available');
      }
    } catch (error) {
      console.error('Error loading project:', error);
      alert(`Error loading project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Filter files based on search query
  const filterFiles = (items: any[], query: string): any[] => {
    if (!query) return items

    return items.reduce((filtered: any[], item) => {
      if (item.type === "folder" || Array.isArray(item.files)) {
        const filteredFiles = filterFiles(item.files || [], query)
        if (filteredFiles.length > 0 || item.name.toLowerCase().includes(query.toLowerCase())) {
          filtered.push({
            ...item,
            expanded: filteredFiles.length > 0 ? true : item.expanded,
            files: filteredFiles,
          })
        }
      } else if (item.name.toLowerCase().includes(query.toLowerCase())) {
        filtered.push(item)
      }
      return filtered
    }, [])
  }

  const filteredProjects = searchQuery ? filterFiles(projects, searchQuery) : projects

  return (
    <div className="h-full bg-editor-sidebar-bg text-editor-sidebar-fg flex flex-col">
      <div className="p-3 border-b border-editor-border">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-medium truncate">Explorer</h2>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-muted-foreground hover:text-foreground"
              onClick={handleOpenProject}
              title="Open existing project"
            >
              <FolderPlus className="h-3.5 w-3.5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-muted-foreground hover:text-foreground"
              onClick={handleCreateProject}
              title="Create new project"
            >
              <Plus className="h-3.5 w-3.5" />
            </Button>
            {projects.length > 0 && (
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 text-muted-foreground hover:text-foreground"
                onClick={handleStartOrchestration}
                title="Orchestrate Taskmaster tasks"
              >
                <Play className="h-3.5 w-3.5" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-muted-foreground hover:text-foreground"
              onClick={() => setShowExplorerSettings(true)}
              title="Explorer settings"
            >
              <Settings className="h-3.5 w-3.5" />
            </Button>
          </div>
        </div>

        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-3.5 w-3.5 text-muted-foreground" />
          <Input
            placeholder="Search files..."
            className="pl-8 h-9 bg-background border-input text-sm"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-2">
          <div className="flex items-center justify-between py-2 px-2 mb-2">
            <span className="text-xs font-medium uppercase tracking-wider text-muted-foreground">Projects</span>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-5 w-5 text-muted-foreground hover:text-foreground"
                onClick={handleOpenProject}
                title="Open existing project"
              >
                <FolderPlus className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-5 w-5 text-muted-foreground hover:text-foreground"
                onClick={handleCreateProject}
                title="Create new project"
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {filteredProjects.length > 0 ? (
            filteredProjects.map((project) => (
              <div key={project.id} className="mb-2">
                <FileTreeItem
                  item={project}
                  onToggle={toggleFolder}
                  onSelect={handleFileSelect}
                  selectedFile={selectedFile}
                />
              </div>
            ))
          ) : (
            <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
              <Folder className="h-16 w-16 text-muted-foreground/20 mb-6" />
              <h3 className="text-sm font-medium text-foreground mb-2">Welcome to CodeFusion</h3>
              <p className="text-xs text-muted-foreground mb-6 max-w-[200px] leading-relaxed">
                Get started by opening an existing project or creating a new one
              </p>
              <div className="flex flex-col gap-3 w-full max-w-[180px]">
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full justify-start h-9"
                  onClick={handleOpenProject}
                >
                  <FolderPlus className="h-4 w-4 mr-2" />
                  Open Project
                </Button>
                <Button
                  size="sm"
                  className="w-full justify-start h-9"
                  onClick={handleCreateProject}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Project
                </Button>
              </div>
            </div>
          )}

          {/* Recent Projects Section */}
          {recentProjects.length > 0 && (
            <div className="mt-4">
              <div className="flex items-center justify-between py-1 px-2 mb-1">
                <span className="text-xs font-medium uppercase tracking-wider text-muted-foreground">Recent</span>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 text-muted-foreground hover:text-foreground"
                      title="Recent projects"
                    >
                      <Clock className="sidebar-icon" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-64">
                    {recentProjects.slice(0, 5).map((project, index) => (
                      <DropdownMenuItem
                        key={`${project.path}-${index}`}
                        onClick={() => switchToProject(project.name, project.path)}
                        className="flex flex-col items-start p-3"
                      >
                        <div className="font-medium text-sm">{project.name}</div>
                        <div className="text-xs text-muted-foreground truncate w-full">
                          {project.path}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(project.lastOpened).toLocaleDateString()}
                        </div>
                      </DropdownMenuItem>
                    ))}
                    {recentProjects.length > 5 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-xs text-muted-foreground">
                          +{recentProjects.length - 5} more projects
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Show first 3 recent projects directly in sidebar */}
              {recentProjects.slice(0, 3).map((project, index) => (
                <div
                  key={`${project.path}-${index}`}
                  className="flex items-center py-1 px-2 text-sm rounded-md cursor-pointer hover:bg-accent/20 group"
                  onClick={() => switchToProject(project.name, project.path)}
                  title={project.path}
                >
                  <Clock className="sidebar-icon mr-1.5 text-muted-foreground flex-shrink-0" />
                  <span className="truncate text-muted-foreground group-hover:text-foreground">
                    {project.name}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      </ScrollArea>

      <div className="p-3 border-t border-editor-border flex items-center justify-between text-xs text-muted-foreground">
        <span>{projects.length} projects</span>
        <span>0 files</span>
      </div>

      {/* Create Project Dialog */}
      <Dialog open={showCreateProjectDialog} onOpenChange={setShowCreateProjectDialog}>
        <DialogContent className="sm:max-w-[425px] h-auto p-6">
          <DialogHeader>
            <DialogTitle>Create New Project</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="project-name" className="text-right text-sm font-medium">
                Name
              </label>
              <Input
                id="project-name"
                value={newProjectName}
                onChange={(e) => setNewProjectName(e.target.value)}
                className="col-span-3"
                placeholder="Enter project name"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    createProject()
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setShowCreateProjectDialog(false)
                setNewProjectName("")
              }}
            >
              Cancel
            </Button>
            <Button type="button" onClick={createProject} disabled={!newProjectName.trim()}>
              Create Project
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Unsaved Changes Dialog */}
      <Dialog open={showUnsavedChangesDialog} onOpenChange={setShowUnsavedChangesDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Unsaved Changes</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              You have unsaved changes in the editor. Would you like to save them before switching projects?
            </p>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleUnsavedChangesConfirm(false)}
            >
              Don't Save
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowUnsavedChangesDialog(false)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={() => handleUnsavedChangesConfirm(true)}
            >
              <Save className="h-4 w-4 mr-2" />
              Save & Switch
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* ✅ PRD Upload Dialog */}
      <Dialog open={showPRDDialog} onOpenChange={setShowPRDDialog}>
        <DialogContent className="sm:max-w-[800px] h-auto overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Upload Project Requirements Document (PRD)</DialogTitle>
            <p className="text-sm text-muted-foreground">
              A valid PRD is required before creating the project. This ensures proper task orchestration and project structure.
            </p>
          </DialogHeader>
          <div className="py-4">
            <PRDUploadUI
              onPRDUploaded={handlePRDUploaded}
              onPRDParsed={handlePRDParsed}
              onValidationChange={handlePRDValidationChange}
              projectPath={currentProjectPath || undefined}
              className="max-h-[60vh] overflow-y-auto"
            />
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setShowPRDDialog(false);
                setCurrentProjectPath(null);
                setPrdValidated(false);
                setNewProjectName("");
              }}
            >
              Cancel Project Creation
            </Button>
            <div className="text-sm text-muted-foreground">
              {prdValidated ?
                "✅ PRD validated. Click 'Parse PRD with Taskmaster' to continue." :
                "❌ Please upload and validate a PRD first."
              }
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* ✅ Taskmaster Orchestration Dialog */}
      <Dialog open={showOrchestrationDialog} onOpenChange={setShowOrchestrationDialog}>
        <DialogContent className="sm:max-w-[800px] h-auto overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Taskmaster Task Orchestration</DialogTitle>
            <p className="text-sm text-muted-foreground">
              Convert Taskmaster tasks into Kanban boards with automatic agent assignments and real-time execution.
            </p>
          </DialogHeader>
          <div className="py-4">
            <TaskmasterOrchestrationUI
              onOrchestrationComplete={handleOrchestrationComplete}
              className="max-h-[60vh] overflow-y-auto"
            />
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowOrchestrationDialog(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Explorer Settings Dialog */}
      <Dialog open={showExplorerSettings} onOpenChange={setShowExplorerSettings}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Explorer Settings</DialogTitle>
            <p className="text-sm text-muted-foreground">
              Configure how files and folders are displayed in the Explorer
            </p>
          </DialogHeader>
          <div className="py-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Display Options</CardTitle>
                <CardDescription>Control what is shown in the file tree</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-hidden">Show Hidden Files</Label>
                  <Switch
                    id="show-hidden"
                    checked={explorerSettings.showHiddenFiles}
                    onCheckedChange={(checked) =>
                      setExplorerSettings(prev => ({ ...prev, showHiddenFiles: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="show-extensions">Show File Extensions</Label>
                  <Switch
                    id="show-extensions"
                    checked={explorerSettings.showFileExtensions}
                    onCheckedChange={(checked) =>
                      setExplorerSettings(prev => ({ ...prev, showFileExtensions: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="show-icons">Show File Icons</Label>
                  <Switch
                    id="show-icons"
                    checked={explorerSettings.showFileIcons}
                    onCheckedChange={(checked) =>
                      setExplorerSettings(prev => ({ ...prev, showFileIcons: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-expand">Auto Expand Folders</Label>
                  <Switch
                    id="auto-expand"
                    checked={explorerSettings.autoExpandFolders}
                    onCheckedChange={(checked) =>
                      setExplorerSettings(prev => ({ ...prev, autoExpandFolders: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="compact-view">Compact View</Label>
                  <Switch
                    id="compact-view"
                    checked={explorerSettings.compactView}
                    onCheckedChange={(checked) =>
                      setExplorerSettings(prev => ({ ...prev, compactView: checked }))
                    }
                  />
                </div>
              </CardContent>
            </Card>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowExplorerSettings(false)}
            >
              Close
            </Button>
            <Button
              type="button"
              onClick={() => {
                // Save settings to localStorage or settings manager
                localStorage.setItem('explorerSettings', JSON.stringify(explorerSettings));
                toast({
                  title: "Settings saved",
                  description: "Explorer settings have been updated successfully.",
                });
                setShowExplorerSettings(false);
              }}
            >
              Save Settings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}